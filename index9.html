<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NCast - Favourite Podcasts</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #ffffff;
            color: #1f1f1f;
            padding: 0 32px;
            max-width: 428px;
            margin: 0 auto;
            min-height: 100vh;
            position: relative;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 24px 0;
        }

        .logo-container {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .logo-icon {
            width: 50px;
            height: 46px;
        }

        .logo-text {
            height: 25px;
        }

        .notification-container {
            position: relative;
        }

        .notification-btn {
            width: 48px;
            height: 48px;
            background-color: rgba(31, 31, 31, 0.1);
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            cursor: pointer;
        }

        .bell-icon {
            width: 21px;
            height: 21px;
            filter: brightness(0) saturate(100%) invert(12%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(95%) contrast(95%);
        }

        .notification-badge {
            position: absolute;
            top: 2px;
            right: 2px;
            width: 12px;
            height: 12px;
            background-color: #ff5757;
            border-radius: 6px;
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 32px;
            color: #1f1f1f;
            line-height: 1.1;
        }

        .podcast-list {
            display: flex;
            flex-direction: column;
            gap: 32px;
            margin-bottom: 120px;
        }

        .podcast-item {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .podcast-cover {
            width: 108px;
            height: 96px;
            border-radius: 16px;
            object-fit: cover;
        }

        .podcast-info {
            flex: 1;
        }

        .podcast-title {
            font-size: 17px;
            font-weight: 600;
            color: #1f1f1f;
            margin-bottom: 6px;
            line-height: 1.2;
        }

        .podcast-category {
            font-size: 14px;
            color: rgba(31, 31, 31, 0.7);
            margin-bottom: 6px;
            line-height: 1.2;
        }

        .podcast-duration {
            font-size: 14px;
            color: rgba(31, 31, 31, 0.7);
            line-height: 1.2;
        }

        .play-btn {
            width: 48px;
            height: 48px;
            background-color: rgba(76, 0, 153, 0.1);
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            cursor: pointer;
        }

        .play-icon {
            width: 14px;
            height: 14px;
        }

        .gradient-overlay {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 156px;
            background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.8) 50%, rgba(255, 255, 255, 1) 100%);
            pointer-events: none;
            z-index: 1;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 428px;
            height: 88px;
            background: rgba(76, 0, 153, 0.1);
            border-radius: 48px 48px 0 0;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 32px;
            backdrop-filter: blur(10px);
            z-index: 2;
        }

        .nav-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            max-width: 300px;
            position: relative;
        }

        .nav-icon {
            width: 32px;
            height: 32px;
            opacity: 0.5;
            cursor: pointer;
            transition: opacity 0.2s;
            filter: brightness(0) saturate(100%) invert(12%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(95%) contrast(95%);
        }

        .nav-icon.active {
            opacity: 1;
            filter: none;
        }

        .nav-indicator {
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 5px;
            height: 5px;
            background-color: #4c0099;
            border-radius: 50%;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo-container">
            <img src="images/ncast-icon.svg" alt="NCast Logo" class="logo-icon">
            <img src="images/ncast-logo-text.svg" alt="NCAST" class="logo-text">
        </div>
        <div class="notification-container">
            <button class="notification-btn">
                <img src="images/bell-icon.png" alt="Notifications" class="bell-icon">
            </button>
            <div class="notification-badge"></div>
        </div>
    </div>

    <h1 class="page-title">Favourite Podcasts</h1>

    <div class="podcast-list">
        <div class="podcast-item">
            <img src="images/podcast-cover-1.png" alt="Sunday Summer - Ep3" class="podcast-cover">
            <div class="podcast-info">
                <div class="podcast-title">Sunday Summer - Ep3</div>
                <div class="podcast-category">Entertainment</div>
                <div class="podcast-duration">15 min</div>
            </div>
            <button class="play-btn">
                <img src="images/play-icon.png" alt="Play" class="play-icon">
            </button>
        </div>

        <div class="podcast-item">
            <img src="images/podcast-cover-2.png" alt="Musical Soul - Vol. 1" class="podcast-cover">
            <div class="podcast-info">
                <div class="podcast-title">Musical Soul - Vol. 1</div>
                <div class="podcast-category">Lifestyle</div>
                <div class="podcast-duration">35 min</div>
            </div>
            <button class="play-btn">
                <img src="images/play-icon.png" alt="Play" class="play-icon">
            </button>
        </div>

        <div class="podcast-item">
            <img src="images/podcast-cover-3.png" alt="Talk Show - Ep4" class="podcast-cover">
            <div class="podcast-info">
                <div class="podcast-title">Talk Show - Ep4</div>
                <div class="podcast-category">Business</div>
                <div class="podcast-duration">20 min</div>
            </div>
            <button class="play-btn">
                <img src="images/play-icon.png" alt="Play" class="play-icon">
            </button>
        </div>

        <div class="podcast-item">
            <img src="images/podcast-cover-4.png" alt="Musical Soul - Vol. 2" class="podcast-cover">
            <div class="podcast-info">
                <div class="podcast-title">Musical Soul - Vol. 2</div>
                <div class="podcast-category">Lifestyle</div>
                <div class="podcast-duration">30 min</div>
            </div>
            <button class="play-btn">
                <img src="images/play-icon.png" alt="Play" class="play-icon">
            </button>
        </div>

        <div class="podcast-item">
            <img src="images/podcast-cover-5.png" alt="Unravelling The Mind" class="podcast-cover">
            <div class="podcast-info">
                <div class="podcast-title">Unravelling The Mind</div>
                <div class="podcast-category">Healthy Lifestyle</div>
                <div class="podcast-duration">10 min</div>
            </div>
            <button class="play-btn">
                <img src="images/play-icon.png" alt="Play" class="play-icon">
            </button>
        </div>

        <div class="podcast-item">
            <img src="images/podcast-cover-6.png" alt="Talk Show - Ep8" class="podcast-cover">
            <div class="podcast-info">
                <div class="podcast-title">Talk Show - Ep8</div>
                <div class="podcast-category">Entertainment</div>
                <div class="podcast-duration">20 min</div>
            </div>
            <button class="play-btn">
                <img src="images/play-icon.png" alt="Play" class="play-icon">
            </button>
        </div>
    </div>

    <div class="gradient-overlay"></div>

    <div class="bottom-nav">
        <div class="nav-container">
            <img src="images/headphones-icon.png" alt="Headphones" class="nav-icon">
            <img src="images/compass-icon.png" alt="Compass" class="nav-icon">
            <div style="position: relative;">
                <img src="images/heart-icon.png" alt="Heart" class="nav-icon active">
                <div class="nav-indicator"></div>
            </div>
            <img src="images/profile-icon.png" alt="Profile" class="nav-icon">
        </div>
    </div>
</body>
</html>
